import { DEBUG, TEST } from '../env'
import { BrowserWindow } from 'electron'
import bridge from './MessageBridge'
import { EventEmitter } from 'events'
import MenuBuilder from './menu'

/**
 * 更新器类 - 负责创建和管理更新窗口
 * 重构后移除了 @electron/remote 依赖，使用现代 Electron IPC 通信
 */
export default class Updater extends EventEmitter {
	private window: BrowserWindow | null = null
	// 保留 dirname 参数以维持 API 兼容性，但当前实现中未使用
	// @ts-ignore: kept for API compatibility
	private readonly _dirname: string

	constructor(dirname: string) {
		super()
		this._dirname = dirname
	}

	/**
	 * 获取更新器窗口实例
	 */
	get win(): BrowserWindow | null {
		return this.window
	}

	/**
	 * 启动更新器窗口
	 */
	start(): void {
		// 创建更新器窗口
		const updateWindow = this.createUpdateWindow()

		// 设置窗口事件监听器
		this.setupWindowEventListeners(updateWindow)

		// 加载更新器页面
		this.loadUpdaterPage(updateWindow)

		// 设置消息代理
		this.setupMessageBridge()

		// 设置菜单
		this.setupWindowMenu(updateWindow)

		// 存储窗口引用
		this.window = updateWindow

		// 开发模式下打开开发者工具
		if (DEBUG || TEST) {
			updateWindow.webContents.openDevTools()
		}
	}

	/**
	 * 关闭更新器窗口
	 */
	close(): void {
		if (this.window && !this.window.isDestroyed()) {
			this.window.close()
		}
	}

	/**
	 * 创建更新器窗口
	 */
	private createUpdateWindow(): BrowserWindow {
		return new BrowserWindow({
			width: 600,
			height: 300,
			resizable: false,
			center: true,
			frame: false,
			autoHideMenuBar: true,
			webPreferences: {
				webSecurity: false,
				nodeIntegration: true,
				contextIsolation: false
				// 移除了 remote 依赖，使用现代 IPC 通信
			}
		})
	}

	/**
	 * 设置窗口事件监听器
	 */
	private setupWindowEventListeners(window: BrowserWindow): void {
		window.on('closed', () => {
			console.log('remove delegate openMainWindow')
			bridge.removeDelegate('openMainWindow')
			this.window = null
			this.emit('window-closed')
		})

		// 监听窗口准备就绪事件
		window.webContents.once('did-finish-load', () => {
			this.emit('window-ready')
		})

		// 监听窗口加载失败事件
		window.webContents.on('did-fail-load', (_event, errorCode, errorDescription) => {
			console.error('Updater window failed to load:', errorCode, errorDescription)
			this.emit('window-load-failed', { errorCode, errorDescription })
		})
	}

	/**
	 * 加载更新器页面
	 */
	private loadUpdaterPage(window: BrowserWindow): void {
		const url = DEBUG ? 'http://localhost:9007' : `updater://updater-dist`
		window.loadURL(url)
	}

	/**
	 * 设置消息代理
	 * 替代原来的 remote.enable 功能，使用 IPC 通信
	 */
	private setupMessageBridge(): void {
		bridge.delegate = {
			openMainWindow: async ({ pack, data }: { pack: string; data: any }) => {
				this.emit('open-main-window', { pack, data })
			}
		}
	}

	/**
	 * 设置窗口菜单
	 */
	private setupWindowMenu(window: BrowserWindow): void {
		const menuBuilder = new MenuBuilder(window)
		menuBuilder.buildMenu()
	}

	/**
	 * 检查窗口是否已销毁
	 */
	isDestroyed(): boolean {
		return !this.window || this.window.isDestroyed()
	}

	/**
	 * 获取窗口ID（如果需要）
	 */
	getWindowId(): number | null {
		return this.window && !this.window.isDestroyed() ? this.window.id : null
	}

	/**
	 * 发送消息到更新器窗口
	 */
	sendMessage(channel: string, ...args: any[]): void {
		if (this.window && !this.window.isDestroyed()) {
			this.window.webContents.send(channel, ...args)
		}
	}

	/**
	 * 显示窗口
	 */
	show(): void {
		if (this.window && !this.window.isDestroyed()) {
			this.window.show()
		}
	}

	/**
	 * 隐藏窗口
	 */
	hide(): void {
		if (this.window && !this.window.isDestroyed()) {
			this.window.hide()
		}
	}

	/**
	 * 聚焦窗口
	 */
	focus(): void {
		if (this.window && !this.window.isDestroyed()) {
			this.window.focus()
		}
	}
}
