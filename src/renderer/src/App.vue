<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import Versions from './components/Versions.vue'
import { electronClient } from './utils/electronClient'
import type { SystemInfo, AppSettings } from '../../method.types'

// 响应式数据
const systemInfo = ref<SystemInfo | null>(null)
const appVersion = ref<string>('')
const settings = ref<AppSettings | null>(null)
const notifications = ref<string[]>([])
const isLoading = ref(false)

// 传统的 IPC 调用（保持向后兼容）
const ipcHandle = (): void => window.electron.ipcRenderer.send('ping')

// 新的 API 调用示例
const loadSystemInfo = async () => {
	try {
		isLoading.value = true
		systemInfo.value = await electronClient.getSystemInfo()
		appVersion.value = await electronClient.getAppVersion()
	} catch (error) {
		console.error('加载系统信息失败:', error)
		notifications.value.push(`错误: ${error.message}`)
	} finally {
		isLoading.value = false
	}
}

const loadSettings = async () => {
	try {
		settings.value = await electronClient.getSettings()
	} catch (error) {
		console.error('加载设置失败:', error)
		notifications.value.push(`错误: ${error.message}`)
	}
}

const toggleTheme = async () => {
	if (!settings.value) return

	try {
		const newTheme = settings.value.theme === 'light' ? 'dark' : 'light'
		await electronClient.updateSettings({ theme: newTheme })
		settings.value.theme = newTheme
		notifications.value.push(`主题已切换为: ${newTheme}`)
	} catch (error) {
		console.error('切换主题失败:', error)
		notifications.value.push(`错误: ${error.message}`)
	}
}

const selectFile = async () => {
	try {
		const filePath = await electronClient.selectFile({
			title: '选择文件',
			filters: [
				{ name: 'Text Files', extensions: ['txt', 'md'] },
				{ name: 'All Files', extensions: ['*'] }
			]
		})

		if (filePath) {
			notifications.value.push(`选择的文件: ${filePath}`)

			// 尝试读取文件内容
			const content = await electronClient.readFile(filePath)
			console.log('文件内容:', content.substring(0, 100) + '...')
		}
	} catch (error) {
		console.error('文件操作失败:', error)
		notifications.value.push(`错误: ${error.message}`)
	}
}

const windowControls = {
	minimize: () => electronClient.minimizeWindow(),
	maximize: () => electronClient.maximizeWindow(),
	close: () => electronClient.closeWindow()
}

// 事件监听器
const eventUnsubscribers: (() => void)[] = []

onMounted(() => {
	// 加载初始数据
	loadSystemInfo()
	loadSettings()

	// 监听应用事件
	eventUnsubscribers.push(
		electronClient.on('app-ready', () => {
			notifications.value.push('应用已准备就绪')
		})
	)

	eventUnsubscribers.push(
		electronClient.on('notification', (data) => {
			notifications.value.push(`通知: ${data.title} - ${data.body}`)
		})
	)

	eventUnsubscribers.push(
		electronClient.on('system-theme-changed', (theme) => {
			notifications.value.push(`系统主题已变更为: ${theme}`)
			if (settings.value) {
				settings.value.theme = theme
			}
		})
	)

	// 发送页面加载事件
	electronClient.emitPageLoaded(window.location.href, document.title, performance.now())
})

onUnmounted(() => {
	// 清理事件监听器
	eventUnsubscribers.forEach((unsubscribe) => unsubscribe())
	electronClient.cleanup()
})
</script>

<template>
	<div class="app">
		<header class="header">
			<img alt="logo" class="logo" src="./assets/electron.svg" />
			<h1>Electron IPC 架构演示</h1>
			<div class="window-controls">
				<button @click="windowControls.minimize" class="control-btn">−</button>
				<button @click="windowControls.maximize" class="control-btn">□</button>
				<button @click="windowControls.close" class="control-btn">×</button>
			</div>
		</header>

		<main class="main">
			<!-- 系统信息区域 -->
			<section class="section">
				<h2>系统信息</h2>
				<div v-if="isLoading" class="loading">加载中...</div>
				<div v-else-if="systemInfo" class="info-grid">
					<div class="info-item">
						<label>平台:</label>
						<span>{{ systemInfo.platform }}</span>
					</div>
					<div class="info-item">
						<label>架构:</label>
						<span>{{ systemInfo.arch }}</span>
					</div>
					<div class="info-item">
						<label>版本:</label>
						<span>{{ systemInfo.version }}</span>
					</div>
					<div class="info-item">
						<label>应用版本:</label>
						<span>{{ appVersion }}</span>
					</div>
					<div class="info-item">
						<label>CPU核心数:</label>
						<span>{{ systemInfo.cpuCount }}</span>
					</div>
					<div class="info-item">
						<label>总内存:</label>
						<span
							>{{ Math.round(systemInfo.totalMemory / 1024 / 1024 / 1024) }} GB</span
						>
					</div>
				</div>
				<button @click="loadSystemInfo" class="btn">刷新系统信息</button>
			</section>

			<!-- 应用设置区域 -->
			<section class="section">
				<h2>应用设置</h2>
				<div v-if="settings" class="settings">
					<div class="setting-item">
						<label>主题:</label>
						<span>{{ settings.theme }}</span>
						<button @click="toggleTheme" class="btn btn-small">切换主题</button>
					</div>
					<div class="setting-item">
						<label>语言:</label>
						<span>{{ settings.language }}</span>
					</div>
					<div class="setting-item">
						<label>自动启动:</label>
						<span>{{ settings.autoStart ? '是' : '否' }}</span>
					</div>
					<div class="setting-item">
						<label>通知:</label>
						<span>{{ settings.notifications ? '开启' : '关闭' }}</span>
					</div>
				</div>
				<button @click="loadSettings" class="btn">刷新设置</button>
			</section>

			<!-- 文件操作区域 -->
			<section class="section">
				<h2>文件操作</h2>
				<div class="file-actions">
					<button @click="selectFile" class="btn">选择文件</button>
				</div>
			</section>

			<!-- 传统IPC测试 -->
			<section class="section">
				<h2>传统 IPC 测试</h2>
				<button @click="ipcHandle" class="btn">发送 Ping</button>
			</section>

			<!-- 通知区域 -->
			<section class="section">
				<h2>事件通知</h2>
				<div class="notifications">
					<div
						v-for="(notification, index) in notifications.slice(-5)"
						:key="index"
						class="notification"
					>
						{{ notification }}
					</div>
					<div v-if="notifications.length === 0" class="no-notifications">暂无通知</div>
				</div>
				<button @click="notifications = []" class="btn btn-small">清空通知</button>
			</section>
		</main>

		<footer class="footer">
			<Versions />
			<p class="tip">按 <code>F12</code> 打开开发者工具查看控制台日志</p>
		</footer>
	</div>
</template>

<style scoped>
.app {
	max-width: 1200px;
	margin: 0 auto;
	padding: 20px;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 30px;
	padding: 20px;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 12px;
	color: white;
}

.header h1 {
	margin: 0;
	font-size: 24px;
	font-weight: 600;
}

.logo {
	height: 60px;
	width: 60px;
	will-change: filter;
	transition: filter 300ms;
}

.logo:hover {
	filter: drop-shadow(0 0 2em #646cffaa);
}

.window-controls {
	display: flex;
	gap: 8px;
}

.control-btn {
	width: 32px;
	height: 32px;
	border: none;
	border-radius: 6px;
	background: rgba(255, 255, 255, 0.2);
	color: white;
	cursor: pointer;
	font-size: 16px;
	font-weight: bold;
	transition: background-color 0.2s;
}

.control-btn:hover {
	background: rgba(255, 255, 255, 0.3);
}

.main {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
	gap: 20px;
	margin-bottom: 30px;
}

.section {
	background: white;
	border-radius: 12px;
	padding: 24px;
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
	border: 1px solid #e5e7eb;
}

.section h2 {
	margin: 0 0 20px 0;
	font-size: 20px;
	font-weight: 600;
	color: #374151;
	border-bottom: 2px solid #e5e7eb;
	padding-bottom: 10px;
}

.loading {
	text-align: center;
	color: #6b7280;
	font-style: italic;
}

.info-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
	gap: 12px;
	margin-bottom: 20px;
}

.info-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12px;
	background: #f9fafb;
	border-radius: 8px;
	border: 1px solid #e5e7eb;
}

.info-item label {
	font-weight: 600;
	color: #374151;
}

.info-item span {
	color: #6b7280;
	font-family: 'Monaco', 'Menlo', monospace;
	font-size: 14px;
}

.settings {
	margin-bottom: 20px;
}

.setting-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 12px 0;
	border-bottom: 1px solid #e5e7eb;
}

.setting-item:last-child {
	border-bottom: none;
}

.setting-item label {
	font-weight: 600;
	color: #374151;
	flex: 1;
}

.setting-item span {
	color: #6b7280;
	margin-right: 12px;
}

.file-actions {
	margin-bottom: 20px;
}

.btn {
	background: #3b82f6;
	color: white;
	border: none;
	padding: 12px 24px;
	border-radius: 8px;
	cursor: pointer;
	font-size: 14px;
	font-weight: 600;
	transition: all 0.2s;
	margin-right: 8px;
	margin-bottom: 8px;
}

.btn:hover {
	background: #2563eb;
	transform: translateY(-1px);
	box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.btn-small {
	padding: 8px 16px;
	font-size: 12px;
}

.notifications {
	max-height: 200px;
	overflow-y: auto;
	margin-bottom: 16px;
}

.notification {
	padding: 12px;
	margin-bottom: 8px;
	background: #f0f9ff;
	border: 1px solid #bae6fd;
	border-radius: 8px;
	color: #0c4a6e;
	font-size: 14px;
}

.no-notifications {
	text-align: center;
	color: #6b7280;
	font-style: italic;
	padding: 20px;
}

.footer {
	text-align: center;
	padding: 20px;
	border-top: 1px solid #e5e7eb;
	background: #f9fafb;
	border-radius: 12px;
}

.tip {
	margin: 16px 0 0 0;
	color: #6b7280;
	font-size: 14px;
}

.tip code {
	background: #e5e7eb;
	padding: 2px 6px;
	border-radius: 4px;
	font-family: 'Monaco', 'Menlo', monospace;
	font-size: 12px;
}
</style>
