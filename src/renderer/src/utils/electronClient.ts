import {
	MainProcessAPI,
	MainProcessEvents,
	RendererProcessEvents,
	ElectronAPI
} from '../../../method.types'

/**
 * Electron 客户端工具类
 * 提供更便捷的API调用和事件处理方法
 */
export class ElectronClient {
	private api: ElectronAPI
	private eventUnsubscribers: Map<string, () => void> = new Map()

	constructor() {
		if (!window.electronAPI) {
			throw new Error('ElectronAPI is not available. Make sure the preload script is loaded.')
		}
		this.api = window.electronAPI
	}

	// ============================================================================
	// API 调用方法
	// ============================================================================

	/**
	 * 调用主进程方法
	 */
	async invoke<K extends keyof MainProcessAPI>(
		method: K,
		...args: Parameters<MainProcessAPI[K]>
	): Promise<ReturnType<MainProcessAPI[K]>> {
		return this.api.invoke(method, ...args)
	}

	// ============================================================================
	// 便捷的API方法
	// ============================================================================

	/**
	 * 获取系统信息
	 */
	async getSystemInfo() {
		return this.invoke('getSystemInfo')
	}

	/**
	 * 获取应用版本
	 */
	async getAppVersion() {
		return this.invoke('getAppVersion')
	}

	/**
	 * 窗口控制
	 */
	async minimizeWindow() {
		return this.invoke('minimizeWindow')
	}

	async maximizeWindow() {
		return this.invoke('maximizeWindow')
	}

	async closeWindow() {
		return this.invoke('closeWindow')
	}

	/**
	 * 文件选择
	 */
	async selectFile(options?: Parameters<MainProcessAPI['selectFile']>[0]) {
		return this.invoke('selectFile', options)
	}

	async selectDirectory(options?: Parameters<MainProcessAPI['selectDirectory']>[0]) {
		return this.invoke('selectDirectory', options)
	}

	/**
	 * 文件操作
	 */
	async readFile(filePath: string) {
		return this.invoke('readFile', filePath)
	}

	async writeFile(filePath: string, content: string) {
		return this.invoke('writeFile', filePath, content)
	}

	/**
	 * 应用设置
	 */
	async getSettings() {
		return this.invoke('getSettings')
	}

	async updateSettings(settings: Parameters<MainProcessAPI['updateSettings']>[0]) {
		return this.invoke('updateSettings', settings)
	}

	/**
	 * HTTP请求
	 */
	async httpRequest(options: Parameters<MainProcessAPI['httpRequest']>[0]) {
		return this.invoke('httpRequest', options)
	}

	/**
	 * 配置管理
	 */
	async getAppPath() {
		return this.invoke('getAppPath')
	}

	async getPackagePath() {
		return this.invoke('getPackagePath')
	}

	async getAssetsPath() {
		return this.invoke('getAssetsPath')
	}

	async getWindowAdapter() {
		return this.invoke('getWindowAdapter')
	}

	/**
	 * 窗口工厂管理
	 */
	async openCustomWindow(options: Parameters<MainProcessAPI['openCustomWindow']>[0]) {
		return this.invoke('openCustomWindow', options)
	}

	async closeCustomWindow(windowId: string) {
		return this.invoke('closeCustomWindow', windowId)
	}

	async moveWindowToLeft(windowId?: string) {
		return this.invoke('moveWindowToLeft', windowId)
	}

	async moveWindowToRight(windowId?: string) {
		return this.invoke('moveWindowToRight', windowId)
	}

	async getActiveWindowId() {
		return this.invoke('getActiveWindowId')
	}

	/**
	 * 更新器管理
	 */
	async startUpdater(dirname: string) {
		return this.invoke('startUpdater', dirname)
	}

	async closeUpdater(updaterId: string) {
		return this.invoke('closeUpdater', updaterId)
	}

	async getUpdaterWindow(updaterId: string) {
		return this.invoke('getUpdaterWindow', updaterId)
	}

	/**
	 * 浏览器代理功能
	 */
	async getTRTCSdkPath() {
		return this.invoke('getTRTCSdkPath')
	}

	async getTRTCLogPath() {
		return this.invoke('getTRTCLogPath')
	}

	async openDevTools() {
		return this.invoke('openDevTools')
	}

	/**
	 * 包管理器功能
	 */
	async getLocalPackageVersion(pack: string) {
		return this.invoke('getLocalPackageVersion', pack)
	}

	async getServerPackageVersion(url: string) {
		return this.invoke('getServerPackageVersion', url)
	}

	async isUpdateAvailable(options: Parameters<MainProcessAPI['isUpdateAvailable']>[0]) {
		return this.invoke('isUpdateAvailable', options)
	}

	async startDownloadTask(options: Parameters<MainProcessAPI['startDownloadTask']>[0]) {
		return this.invoke('startDownloadTask', options)
	}

	async abortDownloadTask(identity: string) {
		return this.invoke('abortDownloadTask', identity)
	}

	async decompressZip(options: Parameters<MainProcessAPI['decompressZip']>[0]) {
		return this.invoke('decompressZip', options)
	}

	// ============================================================================
	// 事件处理方法
	// ============================================================================

	/**
	 * 监听事件
	 */
	on<K extends keyof MainProcessEvents>(
		event: K,
		listener: (data: MainProcessEvents[K]) => void
	): () => void {
		const unsubscribe = this.api.on(event, listener)
		const key = `${event}-${Date.now()}-${Math.random()}`
		this.eventUnsubscribers.set(key, unsubscribe)

		return () => {
			unsubscribe()
			this.eventUnsubscribers.delete(key)
		}
	}

	/**
	 * 一次性监听事件
	 */
	once<K extends keyof MainProcessEvents>(
		event: K,
		listener: (data: MainProcessEvents[K]) => void
	): void {
		this.api.once(event, listener)
	}

	/**
	 * 移除事件监听器
	 */
	off<K extends keyof MainProcessEvents>(
		event: K,
		listener?: (data: MainProcessEvents[K]) => void
	): void {
		this.api.off(event, listener)
	}

	/**
	 * 向主进程发送事件
	 */
	emit<K extends keyof RendererProcessEvents>(event: K, data: RendererProcessEvents[K]): void {
		this.api.emit(event, data)
	}

	// ============================================================================
	// 便捷的事件方法
	// ============================================================================

	/**
	 * 监听应用更新事件
	 */
	onAppUpdate(callback: (updateInfo: MainProcessEvents['app-update-available']) => void) {
		return this.on('app-update-available', callback)
	}

	/**
	 * 监听窗口事件
	 */
	onWindowFocus(callback: () => void) {
		return this.on('window-focus', callback)
	}

	onWindowBlur(callback: () => void) {
		return this.on('window-blur', callback)
	}

	/**
	 * 监听系统主题变化
	 */
	onThemeChange(callback: (theme: MainProcessEvents['system-theme-changed']) => void) {
		return this.on('system-theme-changed', callback)
	}

	/**
	 * 监听通知
	 */
	onNotification(callback: (notification: MainProcessEvents['notification']) => void) {
		return this.on('notification', callback)
	}

	/**
	 * 监听进度更新
	 */
	onProgressUpdate(callback: (progress: MainProcessEvents['progress-update']) => void) {
		return this.on('progress-update', callback)
	}

	// ============================================================================
	// 便捷的发送事件方法
	// ============================================================================

	/**
	 * 发送用户操作事件
	 */
	emitUserAction(action: string, data?: any) {
		this.emit('user-action', {
			action,
			data,
			timestamp: Date.now()
		})
	}

	/**
	 * 发送页面加载事件
	 */
	emitPageLoaded(url: string, title: string, loadTime: number) {
		this.emit('page-loaded', {
			url,
			title,
			loadTime
		})
	}

	/**
	 * 发送错误事件
	 */
	emitError(message: string, stack?: string, code?: string) {
		this.emit('error-occurred', {
			message,
			stack,
			code,
			timestamp: Date.now()
		})
	}

	// ============================================================================
	// 工具方法
	// ============================================================================

	/**
	 * 清理所有事件监听器
	 */
	cleanup(): void {
		this.eventUnsubscribers.forEach((unsubscribe) => unsubscribe())
		this.eventUnsubscribers.clear()
	}

	/**
	 * 检查API是否可用
	 */
	isAvailable(): boolean {
		return !!window.electronAPI
	}
}

// 导出单例实例
export const electronClient = new ElectronClient()

// 导出类型，方便其他地方使用
export type {
	MainProcessAPI,
	MainProcessEvents,
	RendererProcessEvents
} from '../../../method.types'
